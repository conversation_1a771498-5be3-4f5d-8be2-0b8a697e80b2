import telegram
from telegram import Update, <PERSON><PERSON><PERSON><PERSON>boardRemove, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import ContextTypes
from telegram.error import RetryAfter
from config import ADMIN_GROUP_ID, DRIVER_GROUP_ID, ANNOUNCEMENT_GROUPS
from database import save_ride_request, get_user
from keyboards import (
    get_ride_type_keyboard, get_city_keyboard, get_district_keyboard,
    get_time_keyboard, get_date_keyboard, get_yes_no_keyboard, 
    get_passenger_count_keyboard, get_seats_keyboard, get_car_type_keyboard,
    get_car_color_keyboard, get_submit_keyboard, get_contact_keyboard, 
    get_share_contact_keyboard, get_phone_keyboard
)
import json
import asyncio

async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle button presses."""
    query = update.callback_query
    await query.answer()
    
    # Handle cancel button
    if query.data == "cancel_ride":
        # Clear user data
        context.user_data.clear()
        
        # Send confirmation message
        await query.edit_message_text(
            text="❌ Your ride request has been cancelled.\n\n"
                 "Use the buttons below to start again:",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🚗 Create a Ride", callback_data="start_ride")],
                [InlineKeyboardButton("❓ Help", callback_data="show_help")]
            ])
        )
        return
    
    # Handle new start menu buttons
    if query.data == "start_ride":
        # This is equivalent to the /ride command
        await query.edit_message_text(
            text="What would you like to do?",
            reply_markup=get_ride_type_keyboard()
        )
        return
        
    if query.data == "show_help":
        # This is equivalent to the /help command
        await query.edit_message_text(
            text="🚗 *Uzbekistan Rideshare Bot Help*\n\n"
                "*How it works:*\n"
                "1. Create a ride by clicking the 'Create a Ride' button\n"
                "2. Select whether you're a driver or passenger\n"
                "3. Choose your route (Tashkent ↔ Andijan)\n"
                "4. Fill in the details about your trip\n"
                "5. Submit your ride\n\n"
                "*For drivers:* Your offer will be posted in the group\n"
                "*For passengers:* Your request will be sent to admins\n\n"
                "*Privacy Note:* Always use this bot in a private chat for privacy.",
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="back_to_main")]
            ])
        )
        return
        
    if query.data == "back_to_main":
        # Return to main menu
        keyboard = [
            [InlineKeyboardButton("🚗 Create a Ride", callback_data="start_ride")],
            [InlineKeyboardButton("❓ Help", callback_data="show_help")]
        ]
        
        await query.edit_message_text(
            text="👋 Welcome to the Uzbekistan Rideshare Bot!\n\n"
                "Use this bot to offer or request rides between Tashkent and Andijan.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return
    
    # Handle time header buttons (these are just labels, not actual buttons)
    if query.data.startswith("time_header_"):
        return
    
    if query.data == "passenger_request":
        # Start collecting passenger ride details
        context.user_data["ride_type"] = "passenger"
        context.user_data["ride_details"] = {}
        await query.edit_message_text(
            text="You selected: Passenger\n\nPlease select your departure city:",
            reply_markup=get_city_keyboard()
        )
        
    elif query.data == "driver_offer":
        # Start collecting driver ride details
        context.user_data["ride_type"] = "driver"
        context.user_data["ride_details"] = {}
        await query.edit_message_text(
            text="You selected: Driver\n\nPlease select your departure city:",
            reply_markup=get_city_keyboard()
        )
    
    elif query.data.startswith("city_"):
        city = query.data.replace("city_", "")
        context.user_data["ride_details"]["from_city"] = city.capitalize()
        
        # Ask for district
        await query.edit_message_text(
            text=f"Departure: {context.user_data['ride_details']['from_city']}\n\n"
                 f"Please select your district:",
            reply_markup=get_district_keyboard(city)
        )
    
    elif query.data.startswith("district_"):
        district = query.data.replace("district_", "").capitalize()
        context.user_data["ride_details"]["district"] = district
        
        # Set destination city (opposite of starting city)
        from_city = context.user_data["ride_details"]["from_city"]
        if from_city.lower() == "tashkent":
            context.user_data["ride_details"]["to_city"] = "Andijan"
        else:
            context.user_data["ride_details"]["to_city"] = "Tashkent"
        
        # Ask for date (today only)
        await query.edit_message_text(
            text=f"Departure: {from_city} ({district})\n"
                 f"Destination: {context.user_data['ride_details']['to_city']}\n\n"
                 f"Please confirm today as your travel date:",
            reply_markup=get_date_keyboard()
        )
    
    elif query.data.startswith("date_"):
        date = query.data.replace("date_", "")
        context.user_data["ride_details"]["date"] = date
        
        # Ask for time
        time_keyboard = get_time_keyboard()
        if time_keyboard:
            await query.edit_message_text(
                text=f"Departure: {context.user_data['ride_details']['from_city']} ({context.user_data['ride_details']['district']})\n"
                     f"Destination: {context.user_data['ride_details']['to_city']}\n"
                     f"Date: {date}\n\n"
                     f"Please select your travel time:",
                reply_markup=time_keyboard
            )
        else:
            # No future times available today
            await query.edit_message_text(
                text=f"Sorry, there are no more rides available for today. Please try again tomorrow.",
                reply_markup=get_ride_type_keyboard()
            )
            # Clear user data
            context.user_data.clear()
    
    elif query.data.startswith("time_"):
        time = query.data.replace("time_", "")
        context.user_data["ride_details"]["time"] = time
        
        if context.user_data["ride_type"] == "passenger":
            # For passengers, ask for passenger count
            await query.edit_message_text(
                text=f"Departure: {context.user_data['ride_details']['from_city']} ({context.user_data['ride_details']['district']})\n"
                     f"Destination: {context.user_data['ride_details']['to_city']}\n"
                     f"Date: {context.user_data['ride_details']['date']}\n"
                     f"Time: {time}\n\n"
                     f"How many passengers?",
                reply_markup=get_passenger_count_keyboard()
            )
        else:  # driver
            # For drivers, ask for available seats
            await query.edit_message_text(
                text=f"Departure: {context.user_data['ride_details']['from_city']} ({context.user_data['ride_details']['district']})\n"
                     f"Destination: {context.user_data['ride_details']['to_city']}\n"
                     f"Date: {context.user_data['ride_details']['date']}\n"
                     f"Time: {time}\n\n"
                     f"How many seats are available?",
                reply_markup=get_seats_keyboard()
            )
    
    elif query.data.startswith("passengers_"):
        passengers = query.data.replace("passengers_", "")
        context.user_data["ride_details"]["passengers"] = passengers
        
        # Ask about luggage
        await query.edit_message_text(
            text=f"Departure: {context.user_data['ride_details']['from_city']} ({context.user_data['ride_details']['district']})\n"
                 f"Destination: {context.user_data['ride_details']['to_city']}\n"
                 f"Date: {context.user_data['ride_details']['date']}\n"
                 f"Time: {context.user_data['ride_details']['time']}\n"
                 f"Passengers: {passengers}\n\n"
                 f"Do you have luggage?",
            reply_markup=get_yes_no_keyboard("luggage")
        )
    
    elif query.data.startswith("luggage_"):
        has_luggage = query.data.replace("luggage_", "")
        context.user_data["ride_details"]["luggage"] = "Yes" if has_luggage == "yes" else "No"
        
        # Ask for phone number for passenger requests
        await query.edit_message_text(
            text=f"🙋 PASSENGER REQUEST\n\n"
                 f"From: {context.user_data['ride_details'].get('from_city')} ({context.user_data['ride_details'].get('district')})\n"
                 f"To: {context.user_data['ride_details'].get('to_city')}\n"
                 f"Date: {context.user_data['ride_details'].get('date')}\n"
                 f"Time: {context.user_data['ride_details'].get('time')}\n"
                 f"Passengers: {context.user_data['ride_details'].get('passengers')}\n"
                 f"Luggage: {context.user_data['ride_details'].get('luggage')}\n\n"
                 f"Please enter your phone number in the format: +998XXXXXXXXX\n"
                 f"(Type your response below)"
        )
        # Set state to wait for phone number input
        context.user_data["awaiting_phone"] = True
        context.user_data["ride_type_for_phone"] = "passenger"
    
    elif query.data.startswith("seats_"):
        seats = query.data.replace("seats_", "")
        context.user_data["ride_details"]["seats"] = seats
        
        # Ask for car type
        await query.edit_message_text(
            text=f"Departure: {context.user_data['ride_details']['from_city']} ({context.user_data['ride_details']['district']})\n"
                 f"Destination: {context.user_data['ride_details']['to_city']}\n"
                 f"Date: {context.user_data['ride_details']['date']}\n"
                 f"Time: {context.user_data['ride_details']['time']}\n"
                 f"Available seats: {seats}\n\n"
                 f"Please select your car type:",
            reply_markup=get_car_type_keyboard()
        )
    
    elif query.data.startswith("car_"):
        car_type = query.data.replace("car_", "").capitalize()
        context.user_data["ride_details"]["car_type"] = car_type
        
        # Ask for car color
        await query.edit_message_text(
            text=f"Departure: {context.user_data['ride_details']['from_city']} ({context.user_data['ride_details']['district']})\n"
                 f"Destination: {context.user_data['ride_details']['to_city']}\n"
                 f"Date: {context.user_data['ride_details']['date']}\n"
                 f"Time: {context.user_data['ride_details']['time']}\n"
                 f"Available seats: {context.user_data['ride_details']['seats']}\n"
                 f"Car type: {car_type}\n\n"
                 f"Please select your car color:",
            reply_markup=get_car_color_keyboard()
        )
    
    elif query.data.startswith("color_"):
        color = query.data.replace("color_", "")
        car_type = context.user_data["ride_details"].get("car_type", "")
        context.user_data["ride_details"]["car_details"] = f"{color} {car_type}"
        
        # Skip phone number collection and go directly to submission
        message_text = (
            f"🚗 DRIVER OFFER\n\n"
            f"From: {context.user_data['ride_details'].get('from_city')} ({context.user_data['ride_details'].get('district')})\n"
            f"To: {context.user_data['ride_details'].get('to_city')}\n"
            f"Date: {context.user_data['ride_details'].get('date')}\n"
            f"Time: {context.user_data['ride_details'].get('time')}\n"
            f"Available seats: {context.user_data['ride_details'].get('seats')}\n"
            f"Car: {context.user_data['ride_details'].get('car_details')}\n\n"
        )
        
        # Show submit button
        await query.edit_message_text(
            text=message_text + f"Please review and click 'Submit' to confirm:",
            reply_markup=get_submit_keyboard()
        )
    
    elif query.data.startswith("digit_"):
        # Handle digit input for phone number
        digit = query.data.replace("digit_", "")
        
        if "phone_number" not in context.user_data:
            context.user_data["phone_number"] = "+998"
        
        # Add digit to phone number if not complete
        current_phone = context.user_data["phone_number"]
        if len(current_phone) < 13:  # +998 + 9 digits
            context.user_data["phone_number"] = current_phone + digit
        
        # Get ride details for display
        ride_details = context.user_data["ride_details"]
        ride_type = context.user_data.get("ride_type")
        
        # Prepare message text based on ride type
        if ride_type == "passenger":
            message_text = (
                f"🙋 PASSENGER REQUEST\n\n"
                f"From: {ride_details.get('from_city')} ({ride_details.get('district')})\n"
                f"To: {ride_details.get('to_city')}\n"
                f"Date: {ride_details.get('date')}\n"
                f"Time: {ride_details.get('time')}\n"
                f"Passengers: {ride_details.get('passengers')}\n"
                f"Luggage: {ride_details.get('luggage')}\n\n"
            )
        else:  # driver
            message_text = (
                f"🚗 DRIVER OFFER\n\n"
                f"From: {ride_details.get('from_city')} ({ride_details.get('district')})\n"
                f"To: {ride_details.get('to_city')}\n"
                f"Date: {ride_details.get('date')}\n"
                f"Time: {ride_details.get('time')}\n"
                f"Available seats: {ride_details.get('seats')}\n"
                f"Car: {ride_details.get('car_details')}\n\n"
            )
        
        try:
            # Check if phone number is complete
            if len(context.user_data["phone_number"]) == 13:
                # Phone number is complete, save it and show submit button
                ride_details["phone"] = context.user_data["phone_number"]
                
                await query.edit_message_text(
                    text=message_text + 
                         f"Phone: {context.user_data['phone_number']}\n\n"
                         f"Please review and click 'Submit' to confirm:",
                    reply_markup=get_submit_keyboard()
                )
            else:
                # Phone number not complete, continue showing digit keyboard
                await query.edit_message_text(
                    text=message_text + 
                         f"Please enter your phone number:\n"
                         f"Current: {context.user_data['phone_number']}",
                    reply_markup=get_phone_keyboard()
                )
        except telegram.error.RetryAfter as e:
            # If we hit rate limits, just answer the callback query without editing the message
            await query.answer(f"Added digit {digit}. Current: {context.user_data['phone_number']}", show_alert=True)
    
    elif query.data == "backspace":
        # Handle backspace for phone number
        if "phone_number" in context.user_data and len(context.user_data["phone_number"]) > 4:
            # Remove last digit but keep the +998 prefix
            context.user_data["phone_number"] = context.user_data["phone_number"][:-1]
        
        # Get ride details for display
        ride_details = context.user_data["ride_details"]
        ride_type = context.user_data.get("ride_type")
        
        # Prepare message text based on ride type
        if ride_type == "passenger":
            message_text = (
                f"🙋 PASSENGER REQUEST\n\n"
                f"From: {ride_details.get('from_city')} ({ride_details.get('district')})\n"
                f"To: {ride_details.get('to_city')}\n"
                f"Date: {ride_details.get('date')}\n"
                f"Time: {ride_details.get('time')}\n"
                f"Passengers: {ride_details.get('passengers')}\n"
                f"Luggage: {ride_details.get('luggage')}\n\n"
            )
        else:  # driver
            message_text = (
                f"🚗 DRIVER OFFER\n\n"
                f"From: {ride_details.get('from_city')} ({ride_details.get('district')})\n"
                f"To: {ride_details.get('to_city')}\n"
                f"Date: {ride_details.get('date')}\n"
                f"Time: {ride_details.get('time')}\n"
                f"Available seats: {ride_details.get('seats')}\n"
                f"Car: {ride_details.get('car_details')}\n\n"
            )
        
        try:
            # Show updated phone number
            await query.edit_message_text(
                text=message_text + 
                     f"Please enter your phone number:\n"
                     f"Current: {context.user_data['phone_number']}",
                reply_markup=get_phone_keyboard()
            )
        except telegram.error.RetryAfter as e:
            # If we hit rate limits, just answer the callback query without editing the message
            await query.answer(f"Deleted last digit. Current: {context.user_data['phone_number']}", show_alert=True)
    
    elif query.data == "phone_done":
        # User has finished entering their phone number
        if "phone_number" in context.user_data and len(context.user_data["phone_number"]) >= 8:  # At least +998 + 4 digits
            # Save the phone number to ride details
            context.user_data["ride_details"]["phone"] = context.user_data["phone_number"]
            
            # Get ride details for display
            ride_details = context.user_data["ride_details"]
            ride_type = context.user_data.get("ride_type")
            
            # Prepare message text based on ride type
            if ride_type == "passenger":
                message_text = (
                    f"🙋 PASSENGER REQUEST\n\n"
                    f"From: {ride_details.get('from_city')} ({ride_details.get('district')})\n"
                    f"To: {ride_details.get('to_city')}\n"
                    f"Date: {ride_details.get('date')}\n"
                    f"Time: {ride_details.get('time')}\n"
                    f"Passengers: {ride_details.get('passengers')}\n"
                    f"Luggage: {ride_details.get('luggage')}\n"
                    f"Phone: {ride_details.get('phone')}\n\n"
                )
            else:  # driver
                message_text = (
                    f"🚗 DRIVER OFFER\n\n"
                    f"From: {ride_details.get('from_city')} ({ride_details.get('district')})\n"
                    f"To: {ride_details.get('to_city')}\n"
                    f"Date: {ride_details.get('date')}\n"
                    f"Time: {ride_details.get('time')}\n"
                    f"Available seats: {ride_details.get('seats')}\n"
                    f"Car: {ride_details.get('car_details')}\n"
                    f"Phone: {ride_details.get('phone')}\n\n"
                )
            
            try:
                # Show submit button
                await query.edit_message_text(
                    text=message_text + f"Please review and click 'Submit' to confirm:",
                    reply_markup=get_submit_keyboard()
                )
            except RetryAfter as e:
                # If we hit rate limits, just answer the callback query
                await query.answer("Phone number saved. Please wait a moment...", show_alert=True)
                # Try again after a short delay
                await asyncio.sleep(2)
                await query.edit_message_text(
                    text=message_text + f"Please review and click 'Submit' to confirm:",
                    reply_markup=get_submit_keyboard()
                )
        else:
            # Phone number is too short
            await query.answer("Please enter a valid phone number (at least 8 digits)", show_alert=True)
    
    elif query.data == "submit_ride":
        ride_type = context.user_data.get("ride_type")
        ride_details = context.user_data.get("ride_details", {})
        
        # Save user info
        user = get_user(
            query.from_user.id,
            query.from_user.username,
            query.from_user.first_name,
            query.from_user.last_name
        )
        
        # Save to database
        ride_id = save_ride_request(
            query.from_user.id,
            ride_type,
            ride_details
        )
        
        # Create message text based on ride type
        if ride_type == "passenger":
            # Create contact info
            contact_info = f"@{query.from_user.username}" if query.from_user.username else query.from_user.first_name
            phone_info = ride_details.get("phone", "+998 XX XXX XXXX")  # Use collected phone or placeholder
            
            message_text = (
                f"🙋 PASSENGER REQUEST (ID: {ride_id})\n\n"
                f"From: {ride_details.get('from_city')} ({ride_details.get('district')})\n"
                f"To: {ride_details.get('to_city')}\n"
                f"Date: {ride_details.get('date')}\n"
                f"Time: {ride_details.get('time')}\n"
                f"Passengers: {ride_details.get('passengers')}\n"
                f"Luggage: {ride_details.get('luggage')}\n\n"
                f"Contact: {contact_info}\n"
                f"Phone: {phone_info}"
            )
            
            # Forward passenger requests to admin group only
            if ADMIN_GROUP_ID:
                try:
                    await context.bot.send_message(
                        chat_id=ADMIN_GROUP_ID,
                        text=message_text,
                        reply_markup=get_contact_keyboard(query.from_user.id)
                    )
                except Exception as e:
                    print(f"Error sending to admin group: {e}")
            
            await query.edit_message_text(
                text="✅ Your passenger request has been sent to admins!\n\n"
                     "They will contact you if a suitable ride is found.",
                reply_markup=None  # Clear the inline keyboard
            )
        else:  # driver
            # Create contact info
            contact_info = f"@{query.from_user.username}" if query.from_user.username else query.from_user.first_name
            
            message_text = (
                f"🚗 DRIVER OFFER (ID: {ride_id})\n\n"
                f"From: {ride_details.get('from_city')} ({ride_details.get('district')})\n"
                f"To: {ride_details.get('to_city')}\n"
                f"Date: {ride_details.get('date')}\n"
                f"Time: {ride_details.get('time')}\n"
                f"Available seats: {ride_details.get('seats')}\n"
                f"Car: {ride_details.get('car_details')}\n\n"
                f"Contact: {contact_info}"
            )
            
            # Check if the user came from a specific announcement group
            origin_group_id = context.user_data.get("origin_group_id")
            
            if origin_group_id and str(origin_group_id) in ANNOUNCEMENT_GROUPS:
                # Post only to the group the user came from
                try:
                    await context.bot.send_message(
                        chat_id=int(origin_group_id),  # Convert to int to ensure proper format
                        text=message_text,
                        reply_markup=get_contact_keyboard(query.from_user.id)
                    )
                    print(f"Successfully posted to origin group {origin_group_id}")
                except Exception as e:
                    print(f"Error posting to origin group {origin_group_id}: {e}")
                    # If posting to origin group fails, post to driver group as fallback
                    if DRIVER_GROUP_ID:
                        try:
                            await context.bot.send_message(
                                chat_id=DRIVER_GROUP_ID,
                                text=message_text,
                                reply_markup=get_contact_keyboard(query.from_user.id)
                            )
                        except Exception as e:
                            print(f"Error sending to driver group: {e}")
            else:
                # If no origin group or not in ANNOUNCEMENT_GROUPS, post to driver group
                if DRIVER_GROUP_ID:
                    try:
                        await context.bot.send_message(
                            chat_id=DRIVER_GROUP_ID,
                            text=message_text,
                            reply_markup=get_contact_keyboard(query.from_user.id)
                        )
                    except Exception as e:
                        print(f"Error sending to driver group: {e}")
                
                # Also post to all announcement groups if no specific origin
                posted_to_any_group = False
                for group_id in ANNOUNCEMENT_GROUPS:
                    if group_id.strip() and group_id.lstrip('-').isdigit():  # Ensure it's a valid ID
                        try:
                            await context.bot.send_message(
                                chat_id=int(group_id),  # Convert to int
                                text=message_text,
                                reply_markup=get_contact_keyboard(query.from_user.id)
                            )
                            posted_to_any_group = True
                            print(f"Successfully posted to group {group_id}")
                        except Exception as e:
                            print(f"Error posting to group {group_id}: {e}")
            
            await query.edit_message_text(
                text="✅ Your driver offer has been posted!\n\n"
                     "Passengers will contact you directly if interested.",
                reply_markup=None  # Clear the inline keyboard
            )
        
        # Clear user data
        context.user_data.clear()







