# Telegram Rideshare Bot

A Telegram bot for managing rideshare requests between passengers and drivers.

## Features

- Passengers can create ride requests
- Drivers can offer rides
- Admin group for monitoring passenger requests
- Automatic prompting in announcement groups
- SQLite database for persistent storage

## Setup

1. Clone this repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Create a `.env` file based on `.env.example`
4. Get a bot token from [@BotFather](https://t.me/BotFather)
5. Add the bot to your groups and get the group IDs
6. Update the `.env` file with your token and group IDs
7. Run the bot:
   ```
   python main.py
   ```

## Project Structure

- `main.py` - Main entry point
- `config.py` - Configuration settings
- `database.py` - SQLite database operations
- `keyboards.py` - Telegram inline keyboards
- `utils.py` - Utility functions
- `handlers/` - Command, callback, and message handlers

## Database Schema

The bot uses SQLite for data storage with the following tables:

### Rides Table
- `id`: Unique ride identifier
- `user_id`: Telegram user ID
- `ride_type`: Type of ride (passenger/driver)
- `details`: JSON string with ride details
- `status`: Current status of the ride
- `created_at`: Timestamp when the ride was created

### Users Table
- `id`: Unique user identifier
- `user_id`: Telegram user ID
- `username`: Telegram username
- `first_name`: User's first name
- `last_name`: User's last name
- `created_at`: Timestamp when the user was first seen

# Environment Setup

The bot uses a `.env` file for configuration. Before running the bot:

1. Get your bot token from [@BotFather](https://t.me/BotFather)
2. Add your bot to the necessary groups
3. Get the group IDs by adding [@RawDataBot](https://t.me/RawDataBot) to each group
4. Update the `.env` file with your token and group IDs

## Group IDs

Group IDs are negative numbers (e.g., `-1001234567890`). You need to set:

- `ADMIN_GROUP_ID`: Where passenger requests are forwarded
- `DRIVER_GROUP_ID`: Where driver offers are posted
- `ANNOUNCEMENT_GROUPS`: Where the bot monitors messages (comma-separated)
