import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Bot configuration
BOT_TOKEN = os.getenv("BOT_TOKEN")

# Group IDs
ADMIN_GROUP_ID = os.getenv("ADMIN_GROUP_ID")
DRIVER_GROUP_ID = os.getenv("DRIVER_GROUP_ID")

# Process announcement groups, filtering out empty strings
announcement_groups_raw = os.getenv("ANNOUNCEMENT_GROUPS", "")
ANNOUNCEMENT_GROUPS = [group.strip() for group in announcement_groups_raw.split(",") if group.strip()]

# Debug output
print(f"Raw ANNOUNCEMENT_GROUPS from env: '{announcement_groups_raw}'")
print(f"Processed ANNOUNCEMENT_GROUPS: {ANNOUNCEMENT_GROUPS}")

# Database configuration
DB_PATH = os.getenv("DB_PATH", "rideshare.db")


