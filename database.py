import sqlite3
import json
from datetime import datetime
from config import DB_PATH

def get_db_connection():
    """Get a database connection"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """Initialize the database with required tables"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Create rides table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS rides (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        ride_type TEXT NOT NULL,
        details TEXT NOT NULL,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL
    )
    ''')
    
    # Create users table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER UNIQUE NOT NULL,
        username TEXT,
        first_name TEXT,
        last_name TEXT,
        created_at TEXT NOT NULL
    )
    ''')
    
    conn.commit()
    conn.close()

def save_ride_request(user_id, ride_type, details, status="pending"):
    """Save a ride request to the database"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Convert details dict to JSON string
    details_json = json.dumps(details)
    
    cursor.execute(
        "INSERT INTO rides (user_id, ride_type, details, status, created_at) VALUES (?, ?, ?, ?, ?)",
        (user_id, ride_type, details_json, status, datetime.now().isoformat())
    )
    
    ride_id = cursor.lastrowid
    conn.commit()
    conn.close()
    
    return ride_id

def get_user(user_id, username=None, first_name=None, last_name=None):
    """Get user from database or create if not exists"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    cursor.execute("SELECT * FROM users WHERE user_id = ?", (user_id,))
    user = cursor.fetchone()
    
    if not user:
        cursor.execute(
            "INSERT INTO users (user_id, username, first_name, last_name, created_at) VALUES (?, ?, ?, ?, ?)",
            (user_id, username, first_name, last_name, datetime.now().isoformat())
        )
        conn.commit()
        cursor.execute("SELECT * FROM users WHERE user_id = ?", (user_id,))
        user = cursor.fetchone()
    
    conn.close()
    return dict(user) if user else None

def get_ride(ride_id):
    """Get ride details by ID"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    cursor.execute("SELECT * FROM rides WHERE id = ?", (ride_id,))
    ride = cursor.fetchone()
    
    conn.close()
    
    if ride:
        ride_dict = dict(ride)
        # Parse JSON details back to dict
        ride_dict['details'] = json.loads(ride_dict['details'])
        return ride_dict
    
    return None

def update_ride_status(ride_id, status):
    """Update the status of a ride"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    cursor.execute(
        "UPDATE rides SET status = ? WHERE id = ?",
        (status, ride_id)
    )
    
    conn.commit()
    conn.close()
    
    return cursor.rowcount > 0

