
import logging
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CallbackQuer<PERSON><PERSON><PERSON><PERSON>, MessageHandler, filters
from telegram.error import Telegram<PERSON>rror, BadRequest

from config import BOT_TOKEN
from database import init_db
from handlers.commands import start, create_ride_request, help_command, announce
from handlers.callbacks import button_callback
from handlers.messages import handle_group_message, handle_form_input, handle_contact

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', 
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def error_handler(update, context):
    """Log errors caused by updates."""
    logger.error("Exception while handling an update:", exc_info=context.error)
    
    # Handle specific errors
    if isinstance(context.error, BadRequest):
        if "Phone number can be requested in private chats only" in str(context.error):
            # Try to send a message to the user
            try:
                if update and update.effective_chat:
                    await context.bot.send_message(
                        chat_id=update.effective_chat.id,
                        text="⚠️ This feature is only available in private chats. Please use the bot in a private chat."
                    )
            except Exception as e:
                logger.error(f"Failed to send error message: {e}")

def main() -> None:
    """Start the bot."""
    # Initialize database
    init_db()
    
    # Create the Application
    application = ApplicationBuilder().token(BOT_TOKEN).build()

    # Command handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("ride", create_ride_request))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("announce", announce))
    
    # Callback query handler
    application.add_handler(CallbackQueryHandler(button_callback))
    
    # Message handlers - order matters!
    # Handle group messages first with higher priority
    application.add_handler(MessageHandler(
        filters.TEXT & ~filters.COMMAND & (filters.ChatType.GROUP | filters.ChatType.SUPERGROUP), 
        handle_group_message
    ), group=1)  # Group parameter goes here, outside the MessageHandler constructor
    
    # Then handle form input in private chats with lower priority
    application.add_handler(MessageHandler(
        filters.TEXT & ~filters.COMMAND & filters.ChatType.PRIVATE, 
        handle_form_input
    ), group=2)  # Group parameter goes here, outside the MessageHandler constructor
    
    # Register error handler
    application.add_error_handler(error_handler)

    # Start the Bot
    logger.info("Starting bot...")
    application.run_polling()

if __name__ == "__main__":
    main()









