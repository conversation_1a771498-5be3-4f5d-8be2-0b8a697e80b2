from telegram import InlineKeyboardButton, InlineKeyboardMarkup, KeyboardButton, ReplyKeyboardMarkup
from datetime import datetime, timedelta

def get_ride_type_keyboard():
    """Return inline keyboard for selecting ride type."""
    keyboard = [
        [
            InlineKeyboardButton("🚗 I'm a driver", callback_data="driver_offer"),
            InlineKeyboardButton("🙋 I'm a passenger", callback_data="passenger_request"),
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_city_keyboard():
    """Return inline keyboard for selecting city."""
    keyboard = [
        [
            InlineKeyboardButton("Tashkent", callback_data="city_tashkent"),
            InlineKeyboardButton("Andijan", callback_data="city_andijan"),
        ],
        [
            InlineKeyboardButton("❌ Cancel", callback_data="cancel_ride")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_district_keyboard(city):
    """Return inline keyboard for selecting district."""
    if city.lower() == "tashkent":
        districts = [
            ["Chilanzar", "Yunusabad"],
            ["Mirabad", "Yakkasaray"],
            ["Shay<PERSON><PERSON>ur", "Almazar"],
            ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"],
            ["Uchtepa", "Mirzo Ulugbek"],
            ["Yashnabad", "Center"]
        ]
    else:  # Andijan
        districts = [
            ["Center", "Asaka"],
            ["Baliqchi", "Izboskan"],
            ["Jalaquduq", "Khojaabad"],
            ["Marhamat", "Oltinkol"],
            ["Pakhtaabad", "Shahrixon"]
        ]
    
    keyboard = []
    for row in districts:
        keyboard_row = []
        for district in row:
            keyboard_row.append(InlineKeyboardButton(district, callback_data=f"district_{district.lower()}"))
        keyboard.append(keyboard_row)
    
    keyboard.append([InlineKeyboardButton("❌ Cancel", callback_data="cancel_ride")])
    return InlineKeyboardMarkup(keyboard)

def get_time_keyboard():
    """Return inline keyboard for selecting time."""
    current_time = datetime.now()
    current_hour = current_time.hour
    
    # Morning times (6:00 - 11:00)
    morning = [
        ["6:00", "7:00", "8:00"],
        ["9:00", "10:00", "11:00"]
    ]
    
    # Afternoon times (12:00 - 17:00)
    afternoon = [
        ["12:00", "13:00", "14:00"],
        ["15:00", "16:00", "17:00"]
    ]
    
    # Evening times (18:00 - 23:00)
    evening = [
        ["18:00", "19:00", "20:00"],
        ["21:00", "22:00", "23:00"]
    ]
    
    keyboard = []
    
    # Only show future times
    # Add morning times if it's before noon
    if current_hour < 11:
        keyboard.append([InlineKeyboardButton("Morning", callback_data="time_header_morning")])
        for row in morning:
            keyboard_row = []
            for time in row:
                hour = int(time.split(":")[0])
                if hour > current_hour:  # Only show future hours
                    keyboard_row.append(InlineKeyboardButton(time, callback_data=f"time_{time}"))
            if keyboard_row:  # Only add row if it has buttons
                keyboard.append(keyboard_row)
    
    # Add afternoon times if it's before evening
    if current_hour < 17:
        keyboard.append([InlineKeyboardButton("Afternoon", callback_data="time_header_afternoon")])
        for row in afternoon:
            keyboard_row = []
            for time in row:
                hour = int(time.split(":")[0])
                if hour > current_hour:  # Only show future hours
                    keyboard_row.append(InlineKeyboardButton(time, callback_data=f"time_{time}"))
            if keyboard_row:  # Only add row if it has buttons
                keyboard.append(keyboard_row)
    
    # Add evening times if it's before late night
    if current_hour < 23:
        keyboard.append([InlineKeyboardButton("Evening", callback_data="time_header_evening")])
        for row in evening:
            keyboard_row = []
            for time in row:
                hour = int(time.split(":")[0])
                if hour > current_hour:  # Only show future hours
                    keyboard_row.append(InlineKeyboardButton(time, callback_data=f"time_{time}"))
            if keyboard_row:  # Only add row if it has buttons
                keyboard.append(keyboard_row)
    
    # If no future times available today, show a message
    if len(keyboard) == 0:
        return None
    
    keyboard.append([InlineKeyboardButton("❌ Cancel", callback_data="cancel_ride")])
    return InlineKeyboardMarkup(keyboard)

def get_date_keyboard():
    """Return inline keyboard for selecting date."""
    today = datetime.now()
    date_str = today.strftime("%d.%m.%Y")
    
    # Create button for today only
    keyboard = [
        [InlineKeyboardButton(f"Today ({today.strftime('%d.%m')})", callback_data=f"date_{date_str}")],
        [InlineKeyboardButton("❌ Cancel", callback_data="cancel_ride")]
    ]
    
    return InlineKeyboardMarkup(keyboard)

def get_yes_no_keyboard(callback_prefix):
    """Return inline keyboard for yes/no questions."""
    keyboard = [
        [
            InlineKeyboardButton("Yes", callback_data=f"{callback_prefix}_yes"),
            InlineKeyboardButton("No", callback_data=f"{callback_prefix}_no"),
        ],
        [
            InlineKeyboardButton("❌ Cancel", callback_data="cancel_ride")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_passenger_count_keyboard():
    """Return inline keyboard for selecting passenger count."""
    keyboard = [
        [
            InlineKeyboardButton("1", callback_data="passengers_1"),
            InlineKeyboardButton("2", callback_data="passengers_2"),
            InlineKeyboardButton("3", callback_data="passengers_3"),
        ],
        [
            InlineKeyboardButton("4", callback_data="passengers_4"),
            InlineKeyboardButton("5+", callback_data="passengers_5"),
        ],
        [
            InlineKeyboardButton("❌ Cancel", callback_data="cancel_ride")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_seats_keyboard():
    """Return inline keyboard for selecting available seats."""
    keyboard = [
        [
            InlineKeyboardButton("1", callback_data="seats_1"),
            InlineKeyboardButton("2", callback_data="seats_2"),
            InlineKeyboardButton("3", callback_data="seats_3"),
        ],
        [
            InlineKeyboardButton("4", callback_data="seats_4"),
            InlineKeyboardButton("5+", callback_data="seats_5"),
        ],
        [
            InlineKeyboardButton("❌ Cancel", callback_data="cancel_ride")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_car_type_keyboard():
    """Return inline keyboard for selecting car type."""
    keyboard = [
        [
            InlineKeyboardButton("Cobalt", callback_data="car_cobalt"),
            InlineKeyboardButton("Gentra", callback_data="car_gentra"),
        ],
        [
            InlineKeyboardButton("Lacetti", callback_data="car_lacetti"),
            InlineKeyboardButton("Onix", callback_data="car_onix"),
        ],
        [
            InlineKeyboardButton("Kia Sonet", callback_data="car_kia_sonet"),
            InlineKeyboardButton("Captiva", callback_data="car_captiva"),
        ],
        [
            InlineKeyboardButton("Malibu", callback_data="car_malibu"),
            InlineKeyboardButton("Other", callback_data="car_other"),
        ],
        [
            InlineKeyboardButton("❌ Cancel", callback_data="cancel_ride")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_car_color_keyboard():
    """Return inline keyboard for selecting car color."""
    keyboard = [
        [
            InlineKeyboardButton("White", callback_data="color_white"),
            InlineKeyboardButton("Black", callback_data="color_black"),
            InlineKeyboardButton("Silver", callback_data="color_silver"),
        ],
        [
            InlineKeyboardButton("Red", callback_data="color_red"),
            InlineKeyboardButton("Blue", callback_data="color_blue"),
            InlineKeyboardButton("Other", callback_data="color_other"),
        ],
        [
            InlineKeyboardButton("❌ Cancel", callback_data="cancel_ride")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_submit_keyboard():
    """Return inline keyboard for submitting ride details."""
    keyboard = [
        [
            InlineKeyboardButton("✅ Submit", callback_data="submit_ride"),
            InlineKeyboardButton("❌ Cancel", callback_data="cancel_ride"),
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_contact_keyboard(user_id):
    """Return inline keyboard for contacting a user."""
    keyboard = [
        [
            InlineKeyboardButton("📱 Contact", url=f"tg://user?id={user_id}"),
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def get_share_contact_keyboard():
    """Return keyboard with share contact button."""
    keyboard = [
        [KeyboardButton("📱 Share Contact", request_contact=True)],
        [KeyboardButton("❌ Cancel")]
    ]
    return ReplyKeyboardMarkup(keyboard, one_time_keyboard=True, resize_keyboard=True)

def get_phone_keyboard():
    """Return inline keyboard for phone number input."""
    keyboard = [
        [
            InlineKeyboardButton("1", callback_data="digit_1"),
            InlineKeyboardButton("2", callback_data="digit_2"),
            InlineKeyboardButton("3", callback_data="digit_3"),
        ],
        [
            InlineKeyboardButton("4", callback_data="digit_4"),
            InlineKeyboardButton("5", callback_data="digit_5"),
            InlineKeyboardButton("6", callback_data="digit_6"),
        ],
        [
            InlineKeyboardButton("7", callback_data="digit_7"),
            InlineKeyboardButton("8", callback_data="digit_8"),
            InlineKeyboardButton("9", callback_data="digit_9"),
        ],
        [
            InlineKeyboardButton("⬅️ Backspace", callback_data="backspace"),
            InlineKeyboardButton("0", callback_data="digit_0"),
            InlineKeyboardButton("✅ Done", callback_data="phone_done"),
        ],
    ]
    return InlineKeyboardMarkup(keyboard)







