
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from keyboards import get_ride_type_keyboard

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a message when the command /start is issued."""
    # Check if this is a private chat
    if update.effective_chat.type != "private":
        # Store the group ID the user is coming from
        group_id = update.effective_chat.id
        
        # Create deep link to start private chat with the bot
        bot_username = (await context.bot.get_me()).username
        private_chat_link = f"https://t.me/{bot_username}?start=from_group_{group_id}"
        
        await update.message.reply_text(
            "For privacy, please use this bot in a private chat:",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("Start Private Chat", url=private_chat_link)]
            ])
        )
        return
    
    # Handle start command in private chat
    start_param = " ".join(context.args) if context.args else ""
    
    # Check if user came from a specific group
    if start_param.startswith("from_group_"):
        try:
            # Extract the group ID from the parameter
            group_id = start_param.replace("from_group_", "")
            # Store it in user_data for later use
            context.user_data["origin_group_id"] = group_id
        except:
            # If there's an error parsing the group ID, just continue
            pass
    
    # Create inline keyboard with buttons instead of suggesting commands
    keyboard = [
        [InlineKeyboardButton("🚗 Create a Ride", callback_data="start_ride")],
        [InlineKeyboardButton("❓ Help", callback_data="show_help")]
    ]
    
    await update.message.reply_text(
        "👋 Welcome to the Uzbekistan Rideshare Bot!\n\n"
        "Use this bot to offer or request rides between Tashkent and Andijan.",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def create_ride_request(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Start the ride creation process."""
    # Check if this is a private chat
    if update.effective_chat.type != "private":
        # Create deep link to start private chat with the bot
        bot_username = (await context.bot.get_me()).username
        private_chat_link = f"https://t.me/{bot_username}?start=ride"
        
        await update.message.reply_text(
            "For privacy, please use this command in a private chat:",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("Start Private Chat", url=private_chat_link)]
            ])
        )
        return
    
    # Handle ride command in private chat
    await update.message.reply_text(
        "What would you like to do?",
        reply_markup=get_ride_type_keyboard()
    )

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send help information."""
    # Check if this is a private chat
    if update.effective_chat.type != "private":
        # Create deep link to start private chat with the bot
        bot_username = (await context.bot.get_me()).username
        private_chat_link = f"https://t.me/{bot_username}?start=help"
        
        await update.message.reply_text(
            "For privacy, please use this command in a private chat:",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("Start Private Chat", url=private_chat_link)]
            ])
        )
        return
    
    # Handle help command in private chat
    await update.message.reply_text(
        "🚗 *Uzbekistan Rideshare Bot Help*\n\n"
        "*Commands:*\n"
        "/ride - Create a new ride offer or request\n"
        "/help - Show this help message\n\n"
        "*How it works:*\n"
        "1. Use /ride to start creating a ride\n"
        "2. Select whether you're a driver or passenger\n"
        "3. Choose your route (Tashkent ↔ Andijan)\n"
        "4. Fill in the details about your trip\n"
        "5. Submit your ride\n\n"
        "*For drivers:* Your offer will be posted in the group\n"
        "*For passengers:* Your request will be sent to admins\n\n"
        "*Privacy Note:* Always use this bot in a private chat for privacy.",
        parse_mode="Markdown"
    )

async def announce(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send an announcement to the group (admin only)."""
    # Check if this is a group chat
    if update.effective_chat.type not in ["group", "supergroup"]:
        await update.message.reply_text(
            "This command can only be used in groups."
        )
        return
    
    # Check if the user is an admin
    user_id = update.effective_user.id
    chat_id = update.effective_chat.id
    
    try:
        # Get chat administrators
        admins = await context.bot.get_chat_administrators(chat_id)
        admin_ids = [admin.user.id for admin in admins]
        
        if user_id in admin_ids:
            # User is an admin, allow them to post an announcement
            announcement_text = " ".join(context.args)
            
            if not announcement_text:
                await update.message.reply_text(
                    "Please provide an announcement text after the command.\n"
                    "Example: /announce Important message for all members."
                )
                return
            
            # Delete the command message
            await context.bot.delete_message(
                chat_id=chat_id,
                message_id=update.message.message_id
            )
            
            # Send the announcement
            await context.bot.send_message(
                chat_id=chat_id,
                text=f"📢 *ANNOUNCEMENT*\n\n{announcement_text}",
                parse_mode="Markdown"
            )
        else:
            # User is not an admin
            await update.message.reply_text(
                "Only administrators can use this command."
            )
    except Exception as e:
        print(f"Error in announce command: {e}")
        await update.message.reply_text(
            "An error occurred while processing your request."
        )





