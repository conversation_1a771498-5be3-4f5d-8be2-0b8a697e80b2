
from telegram import Update, <PERSON>ly<PERSON>eyboardRemove, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from config import ADMIN_GROUP_ID, DRIVER_GROUP_ID, ANNOUNCEMENT_GROUPS
from keyboards import get_ride_type_keyboard, get_submit_keyboard
from database import save_ride_request, get_user
import re

async def handle_group_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle messages in groups."""
    # Debug logging
    print(f"Group message received in chat ID: {update.effective_chat.id}")
    print(f"Message text: {update.message.text}")
    print(f"ANNOUNCEMENT_GROUPS: {ANNOUNCEMENT_GROUPS}")
    
    # Convert both to strings for comparison
    chat_id_str = str(update.effective_chat.id)
    announcement_groups_str = [str(group) for group in ANNOUNCEMENT_GROUPS]
    
    print(f"Is in announcement groups: {chat_id_str in announcement_groups_str}")
    
    # Check if this is an announcement group
    if chat_id_str in announcement_groups_str:
        print(f"Processing message in announcement group: {chat_id_str}")
        
        try:
            # Store the group ID the user is coming from
            group_id = update.effective_chat.id
            user_id = update.effective_user.id
            
            # Create deep link to start private chat with the bot
            bot_username = (await context.bot.get_me()).username
            private_chat_link = f"https://t.me/{bot_username}?start=from_group_{group_id}"
            
            # Try to delete the original message if the bot has permission
            try:
                await context.bot.delete_message(
                    chat_id=update.effective_chat.id,
                    message_id=update.message.message_id
                )
                print(f"Deleted message from user {user_id} in group {chat_id_str}")
            except Exception as e:
                print(f"Could not delete message: {e}")
            
            # Respond with the same message as the /start command would in a group
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=f"👋 @{update.effective_user.username or update.effective_user.first_name}, this group is for ride sharing between Tashkent and Andijan.\n\n"
                     f"For privacy, please use this bot in a private chat:",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("Start Private Chat", url=private_chat_link)]
                ])
            )
            print(f"Successfully sent response in group {chat_id_str}")
        except Exception as e:
            print(f"Error responding to message in group {chat_id_str}: {e}")
    else:
        print(f"Message in non-announcement group: {chat_id_str}")

async def handle_form_input(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle form input in private chats."""
    # Check if we're waiting for a phone number
    if context.user_data.get("awaiting_phone"):
        # Get the phone number from the message
        phone_number = update.message.text.strip()
        
        # Validate phone number format (simple validation)
        phone_pattern = re.compile(r'^\+?[0-9]{10,13}$')
        
        if phone_pattern.match(phone_number):
            # Add country code if not provided
            if not phone_number.startswith("+"):
                phone_number = "+998" + phone_number.lstrip("0")
            elif not phone_number.startswith("+998"):
                # If it has a plus but not +998, assume it's a different country code
                # We'll keep it as is
                pass
                
            # Save the phone number
            context.user_data["ride_details"]["phone"] = phone_number
            
            # Clear the awaiting state
            context.user_data.pop("awaiting_phone", None)
            
            # Get ride details for display
            ride_details = context.user_data["ride_details"]
            ride_type = context.user_data.get("ride_type_for_phone")
            
            # Prepare message text based on ride type
            if ride_type == "passenger":
                message_text = (
                    f"🙋 PASSENGER REQUEST\n\n"
                    f"From: {ride_details.get('from_city')} ({ride_details.get('district')})\n"
                    f"To: {ride_details.get('to_city')}\n"
                    f"Date: {ride_details.get('date')}\n"
                    f"Time: {ride_details.get('time')}\n"
                    f"Passengers: {ride_details.get('passengers')}\n"
                    f"Luggage: {ride_details.get('luggage')}\n"
                    f"Phone: {ride_details.get('phone')}\n\n"
                )
            else:  # driver
                message_text = (
                    f"🚗 DRIVER OFFER\n\n"
                    f"From: {ride_details.get('from_city')} ({ride_details.get('district')})\n"
                    f"To: {ride_details.get('to_city')}\n"
                    f"Date: {ride_details.get('date')}\n"
                    f"Time: {ride_details.get('time')}\n"
                    f"Available seats: {ride_details.get('seats')}\n"
                    f"Car: {ride_details.get('car_details')}\n"
                    f"Phone: {ride_details.get('phone')}\n\n"
                )
            
            # Show submit button
            await update.message.reply_text(
                text=message_text + f"Please review and click 'Submit' to confirm:",
                reply_markup=get_submit_keyboard()
            )
        else:
            # Invalid phone number format
            await update.message.reply_text(
                "Please enter a valid phone number (e.g., +998901234567 or 901234567)."
            )
            # Keep waiting for a valid phone number
            return

async def handle_contact(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle contact sharing."""
    # This function is no longer needed as we're not collecting contacts
    pass



















